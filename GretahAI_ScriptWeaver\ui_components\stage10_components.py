"""
Stage 10 UI Components for GretahAI ScriptWeaver

Minimalist UI components for Script Template Manager with professional enterprise styling.
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Import core dependencies
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_ui")


def render_empty_playground_message():
    """
    Render the empty playground message.
    """
    st.info("🎮 **No Templates Available**")
    st.markdown("Complete Stage 8 to create optimized script templates.")

    col1, col2, col3 = st.columns(3)
    return col1, col2, col3


def render_no_test_cases_message():
    """
    Render the no test cases available message.
    """
    st.warning("⚠️ **No Test Cases Available**")
    st.markdown("Upload a CSV file with test cases first.")


def render_template_selection_interface(optimized_scripts, template_map):
    """
    Render the template selection interface.

    Args:
        optimized_scripts: List of available optimized scripts
        template_map: Mapping of display options to script objects

    Returns:
        tuple: (selected_template, template_options) or (None, None) if no templates
    """
    with st.expander("🎯 Template Selection", expanded=True):
        if not optimized_scripts:
            st.info("No templates available.")
            return None, None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_template_script_display

        # Create template options
        template_options = []
        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)

        if not template_options:
            st.info("No template options available.")
            return None, None

        selected_template_option = st.selectbox(
            "Template",
            template_options,
            key="template_selection"
        )

        selected_template = template_map[selected_template_option]

        # Display template details
        _render_template_details_columns(selected_template)

        # Template preview toggle
        if st.checkbox("📄 Preview", key="show_template_preview"):
            template_content = selected_template.get('content', 'No content available')
            st.code(template_content, language='python')

        return selected_template, template_options


def _render_template_details_columns(selected_template):
    """
    Render template details in two-column layout.

    Args:
        selected_template: Selected template script object
    """
    from core.template_helpers import format_template_script_display

    display_info = format_template_script_display(selected_template)

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"""
        **Details:**
        - Test Case: {selected_template.get('test_case_id', 'Unknown')}
        - Created: {display_info['timestamp']}
        - Size: {display_info['size_info']}
        """)

    with col2:
        st.info(f"""
        **Status:**
        - ✅ Optimized
        - Type: {selected_template.get('type', 'Unknown').title()}
        - {display_info['optimization_info']}
        """)


def render_test_case_selection_interface(available_test_cases, test_case_map):
    """
    Render the test case selection interface.

    Args:
        available_test_cases: List of available test cases
        test_case_map: Mapping of display options to test case objects

    Returns:
        selected_test_case or None
    """
    with st.expander("📋 Target Test Case", expanded=True):
        if not available_test_cases:
            st.info("No test cases available.")
            return None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_test_case_display

        # Create test case options
        test_case_options = []
        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)

        if not test_case_options:
            st.info("No test case options available.")
            return None

        selected_test_case_option = st.selectbox(
            "Test Case",
            test_case_options,
            key="test_case_selection"
        )

        selected_test_case = test_case_map[selected_test_case_option]

        # Display test case details
        _render_test_case_details(selected_test_case)

        return selected_test_case


def _render_test_case_details(selected_test_case):
    """
    Render test case details.

    Args:
        selected_test_case: Selected test case object
    """
    tc_id = selected_test_case.get('Test Case ID', 'Unknown')
    tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
    tc_steps = selected_test_case.get('Steps', [])

    st.info(f"""
    **Target:**
    - ID: {tc_id}
    - Objective: {tc_objective}
    - Steps: {len(tc_steps)}
    """)


def render_gap_analysis_interface(selected_template, selected_test_case):
    """
    Render the gap analysis interface that analyzes differences between template and test case.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        dict: Gap analysis results or None if analysis not completed
    """
    with st.expander("🔍 Gap Analysis", expanded=True):
        # Validate inputs first
        from core.template_helpers import validate_template_generation_inputs

        is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

        if not is_valid:
            st.error(f"❌ {error_message}")
            return None

        st.markdown("**Analyze template compatibility with your test case requirements**")

        # Check if gap analysis has been performed for this combination
        gap_analysis_key = f"gap_analysis_{selected_template.get('id', 'unknown')}_{selected_test_case.get('Test Case ID', 'unknown')}"

        # Gap analysis controls
        col1, col2 = st.columns([3, 1])

        with col1:
            st.info("🤖 AI will analyze template coverage and identify missing requirements")

        with col2:
            analyze_clicked = st.button("🔍 Analyze", use_container_width=True, type="primary")

        # Display existing gap analysis results if available
        if gap_analysis_key in st.session_state:
            gap_analysis_data = st.session_state[gap_analysis_key]
            _render_gap_analysis_results(gap_analysis_data)
            return gap_analysis_data

        # Perform gap analysis if button clicked
        if analyze_clicked:
            with st.spinner("🤖 Analyzing template coverage..."):
                gap_analysis_data = _perform_gap_analysis(selected_template, selected_test_case)
                if gap_analysis_data:
                    st.session_state[gap_analysis_key] = gap_analysis_data
                    st.rerun()

        return None


def render_gap_filling_form(gap_analysis_data):
    """
    Render dynamic form for filling identified gaps.

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        dict: User-provided gap filling data or None
    """
    if not gap_analysis_data or not gap_analysis_data.get('gaps_identified'):
        return None

    with st.expander("📝 Fill Gaps", expanded=True):
        st.markdown("**Provide additional information to address identified gaps**")

        gaps = gap_analysis_data.get('gaps', [])
        gap_responses = {}

        if gaps:
            for i, gap in enumerate(gaps):
                gap_type = gap.get('type', 'general')
                gap_description = gap.get('description', 'No description')
                gap_prompt = gap.get('prompt', f"Please provide information for: {gap_description}")

                st.markdown(f"**Gap {i+1}: {gap_type.title()}**")
                st.markdown(f"*{gap_description}*")

                # Create appropriate input based on gap type
                if gap_type == 'missing_steps':
                    response = st.text_area(
                        gap_prompt,
                        placeholder="Describe the missing test steps...",
                        key=f"gap_response_{i}",
                        height=100
                    )
                elif gap_type == 'missing_data':
                    response = st.text_input(
                        gap_prompt,
                        placeholder="Provide the missing data...",
                        key=f"gap_response_{i}"
                    )
                elif gap_type == 'customization':
                    response = st.text_area(
                        gap_prompt,
                        placeholder="Specify customization requirements...",
                        key=f"gap_response_{i}",
                        height=80
                    )
                else:
                    response = st.text_area(
                        gap_prompt,
                        placeholder="Provide additional context...",
                        key=f"gap_response_{i}",
                        height=80
                    )

                if response:
                    gap_responses[f"gap_{i}"] = {
                        'type': gap_type,
                        'description': gap_description,
                        'response': response
                    }

                st.markdown("---")

        # Additional custom instructions
        st.markdown("**Additional Instructions (Optional)**")
        additional_instructions = st.text_area(
            "Any other specific requirements or modifications?",
            placeholder="Optional additional instructions...",
            key="gap_additional_instructions",
            height=60
        )

        if additional_instructions:
            gap_responses['additional_instructions'] = additional_instructions

        return gap_responses if gap_responses else None


def render_script_generation_controls(selected_template, selected_test_case, gap_analysis_data=None, gap_responses=None):
    """
    Render the script generation controls interface with gap analysis integration.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case
        gap_analysis_data: Gap analysis results (optional)
        gap_responses: User responses to fill gaps (optional)

    Returns:
        tuple: (custom_instructions, preserve_structure, include_error_handling, generate_clicked, enhanced_context)
    """
    with st.expander("🤖 Generation", expanded=True):
        # Validate inputs
        from core.template_helpers import validate_template_generation_inputs

        is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

        if not is_valid:
            st.error(f"❌ {error_message}")
            return None, None, None, False, None

        # Show gap analysis status if available
        if gap_analysis_data:
            gaps_count = len(gap_analysis_data.get('gaps', []))
            filled_count = len(gap_responses) if gap_responses else 0

            if gaps_count > 0:
                if filled_count > 0:
                    st.success(f"✅ Gap analysis complete: {filled_count}/{gaps_count} gaps addressed")
                else:
                    st.warning(f"⚠️ {gaps_count} gaps identified - consider filling gaps for better results")
            else:
                st.success("✅ No significant gaps identified - template is well-suited")

        # Generation controls
        col1, col2 = st.columns(2)

        with col1:
            custom_instructions = st.text_area(
                "Custom Instructions",
                placeholder="Optional modifications...",
                key="template_custom_instructions"
            )

        with col2:
            st.markdown("**Settings:**")
            preserve_structure = st.checkbox("Preserve Structure", value=True, key="preserve_structure")
            include_error_handling = st.checkbox("Error Handling", value=True, key="include_error_handling")

        # Prepare enhanced context from gap analysis
        enhanced_context = None
        if gap_analysis_data and gap_responses:
            enhanced_context = {
                'gap_analysis': gap_analysis_data,
                'gap_responses': gap_responses
            }

        # Generate button
        generate_clicked = st.button("🚀 Generate Script", use_container_width=True, type="primary")

        return custom_instructions, preserve_structure, include_error_handling, generate_clicked, enhanced_context


def _render_gap_analysis_results(gap_analysis_data):
    """
    Render the gap analysis results display.

    Args:
        gap_analysis_data: Gap analysis results from AI
    """
    if not gap_analysis_data:
        return

    st.markdown("### 📊 Analysis Results")

    # Overall assessment
    overall_assessment = gap_analysis_data.get('overall_assessment', 'No assessment available')
    compatibility_score = gap_analysis_data.get('compatibility_score', 'Unknown')

    # Display compatibility score with color coding
    if isinstance(compatibility_score, (int, float)):
        if compatibility_score >= 80:
            score_color = "#4CAF50"  # Green
            score_icon = "✅"
        elif compatibility_score >= 60:
            score_color = "#FF9800"  # Orange
            score_icon = "⚠️"
        else:
            score_color = "#F44336"  # Red
            score_icon = "❌"

        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, rgba({','.join(str(int(score_color[i:i+2], 16)) for i in (1, 3, 5))}, 0.1), rgba(255,255,255,0.05));
            border: 1px solid {score_color}40;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        ">
            <span style="font-size: 1.2rem;">{score_icon}</span>
            <span style="color: {score_color}; font-weight: 600;">Compatibility: {compatibility_score}%</span>
        </div>
        """, unsafe_allow_html=True)

    # Overall assessment
    st.markdown(f"**Assessment:** {overall_assessment}")

    # Display identified gaps
    gaps = gap_analysis_data.get('gaps', [])
    gaps_identified = gap_analysis_data.get('gaps_identified', False)

    if gaps_identified and gaps:
        st.markdown(f"### 🔍 Identified Gaps ({len(gaps)})")

        for i, gap in enumerate(gaps):
            gap_type = gap.get('type', 'general')
            gap_description = gap.get('description', 'No description')
            gap_severity = gap.get('severity', 'medium')

            # Color code by severity
            severity_colors = {
                'high': '#F44336',
                'medium': '#FF9800',
                'low': '#2196F3'
            }
            severity_color = severity_colors.get(gap_severity, '#2196F3')

            st.markdown(f"""
            <div style="
                border-left: 4px solid {severity_color};
                background: rgba({','.join(str(int(severity_color[i:i+2], 16)) for i in (1, 3, 5))}, 0.05);
                padding: 1rem;
                margin: 0.5rem 0;
                border-radius: 0 8px 8px 0;
            ">
                <strong>{gap_type.title()}</strong> <span style="color: {severity_color}; font-size: 0.8rem;">({gap_severity.upper()})</span><br>
                <span style="color: var(--text-color-light);">{gap_description}</span>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.success("✅ No significant gaps identified - template appears well-suited for this test case")

    # Recommendations
    recommendations = gap_analysis_data.get('recommendations', [])
    if recommendations:
        st.markdown("### 💡 Recommendations")
        for rec in recommendations:
            st.markdown(f"• {rec}")


def _perform_gap_analysis(selected_template, selected_test_case):
    """
    Perform AI-powered gap analysis between template and test case.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        dict: Gap analysis results
    """
    try:
        # Import AI function inside to avoid circular imports
        from core.ai import generate_llm_response
        from debug_utils import debug

        debug("Starting gap analysis between template and test case")

        # Build gap analysis prompt
        gap_analysis_prompt = _build_gap_analysis_prompt(selected_template, selected_test_case)

        # Call Google AI for gap analysis
        debug("Calling Google AI for gap analysis")
        response = generate_llm_response(
            prompt=gap_analysis_prompt,
            model_name="gemini-2.0-flash",
            api_key=st.session_state.get('state', {}).google_api_key if hasattr(st.session_state.get('state', {}), 'google_api_key') else None,
            category="gap_analysis",
            context={
                'template_test_case_id': selected_template.get('test_case_id', 'unknown'),
                'target_test_case_id': selected_test_case.get('Test Case ID', 'unknown'),
                'analysis_type': 'template_gap_analysis'
            }
        )

        if response:
            # Parse the AI response into structured gap analysis data
            gap_analysis_data = _parse_gap_analysis_response(response)
            debug(f"Gap analysis completed: {len(gap_analysis_data.get('gaps', []))} gaps identified")
            return gap_analysis_data
        else:
            st.error("❌ Failed to get response from AI for gap analysis")
            return None

    except Exception as e:
        debug(f"Error performing gap analysis: {e}")
        st.error(f"❌ Error during gap analysis: {str(e)}")
        return None


def _build_gap_analysis_prompt(selected_template, selected_test_case):
    """
    Build the prompt for AI gap analysis.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        str: Gap analysis prompt
    """
    template_content = selected_template.get('content', '')
    template_test_case_id = selected_template.get('test_case_id', 'Unknown')

    target_tc_id = selected_test_case.get('Test Case ID', 'Unknown')
    target_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
    target_steps = selected_test_case.get('Steps', [])

    prompt = f"""# Template Gap Analysis

You are analyzing the compatibility between a template script and a target test case to identify gaps that need to be addressed for successful script generation.

## Template Information
- **Template Test Case ID**: {template_test_case_id}
- **Template Script Length**: {len(template_content)} characters

## Target Test Case
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}

### Target Test Steps:
"""

    # Add target test steps
    for i, step in enumerate(target_steps, 1):
        step_desc = step.get('step_description', step.get('Step Description', 'No description'))
        prompt += f"{i}. {step_desc}\n"

    prompt += f"""

## Template Script (for reference):
```python
{template_content[:2000]}{'...' if len(template_content) > 2000 else ''}
```

## Analysis Instructions

Analyze the template script against the target test case and identify:

1. **Missing Test Steps**: Steps in the target test case that are not covered by the template
2. **Missing Data**: Test data, parameters, or values that the template doesn't handle
3. **Customization Needs**: Template elements that need modification for the target test case
4. **Locator Gaps**: UI elements or locators that may not be covered by the template

## Output Format

Provide your analysis in the following JSON format:

```json
{{
    "compatibility_score": <number 0-100>,
    "overall_assessment": "<brief overall assessment>",
    "gaps_identified": <true/false>,
    "gaps": [
        {{
            "type": "<missing_steps|missing_data|customization|locator_gaps>",
            "severity": "<high|medium|low>",
            "description": "<detailed description of the gap>",
            "prompt": "<user-friendly prompt to collect missing information>"
        }}
    ],
    "recommendations": [
        "<actionable recommendation 1>",
        "<actionable recommendation 2>"
    ]
}}
```

Focus on practical gaps that would impact script generation quality. Be specific and actionable in your gap descriptions and prompts.
"""

    return prompt


def _parse_gap_analysis_response(response):
    """
    Parse the AI response for gap analysis into structured data.

    Args:
        response: Raw AI response text

    Returns:
        dict: Parsed gap analysis data
    """
    try:
        import json
        import re
        from debug_utils import debug

        debug(f"Parsing gap analysis response: {len(response)} characters")

        # Try to extract JSON from the response
        # Look for JSON block in markdown format
        json_match = re.search(r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find JSON without markdown formatting
            json_match = re.search(r'(\{.*\})', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Fallback: create basic structure from text analysis
                debug("No JSON found in response, creating fallback structure")
                return _create_fallback_gap_analysis(response)

        # Parse the JSON
        gap_data = json.loads(json_str)

        # Validate and clean the data
        validated_data = {
            'compatibility_score': gap_data.get('compatibility_score', 75),
            'overall_assessment': gap_data.get('overall_assessment', 'Analysis completed'),
            'gaps_identified': gap_data.get('gaps_identified', False),
            'gaps': gap_data.get('gaps', []),
            'recommendations': gap_data.get('recommendations', [])
        }

        # Ensure gaps have required fields
        for gap in validated_data['gaps']:
            if 'type' not in gap:
                gap['type'] = 'general'
            if 'severity' not in gap:
                gap['severity'] = 'medium'
            if 'description' not in gap:
                gap['description'] = 'Gap identified'
            if 'prompt' not in gap:
                gap['prompt'] = f"Please provide information for: {gap['description']}"

        debug(f"Successfully parsed gap analysis: {len(validated_data['gaps'])} gaps")
        return validated_data

    except Exception as e:
        debug(f"Error parsing gap analysis response: {e}")
        return _create_fallback_gap_analysis(response)


def _create_fallback_gap_analysis(response):
    """
    Create a fallback gap analysis structure when JSON parsing fails.

    Args:
        response: Raw AI response text

    Returns:
        dict: Basic gap analysis structure
    """
    # Simple text analysis to determine if gaps were mentioned
    response_lower = response.lower()
    gaps_mentioned = any(keyword in response_lower for keyword in [
        'gap', 'missing', 'need', 'require', 'lack', 'absent', 'incomplete'
    ])

    return {
        'compatibility_score': 70,  # Conservative estimate
        'overall_assessment': 'Analysis completed with basic text parsing',
        'gaps_identified': gaps_mentioned,
        'gaps': [{
            'type': 'general',
            'severity': 'medium',
            'description': 'Manual review recommended - automated analysis encountered issues',
            'prompt': 'Please review the template and test case manually and provide any additional requirements'
        }] if gaps_mentioned else [],
        'recommendations': ['Consider manual review of template compatibility']
    }


def render_script_execution_section_header():
    """
    Render the script execution section header.
    """
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0;">
        <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem;">
            🧪 Test Script
        </h2>
    </div>
    """, unsafe_allow_html=True)


def render_script_info_card(script_data, target_test_case, execution_status):
    """
    Render script information card with status indicators.

    Args:
        script_data: Script data from session state
        target_test_case: Target test case information
        execution_status: Current execution status (ready, passed, failed)
    """
    filename = script_data.get('filename', 'Unknown')
    tc_id = target_test_case.get('Test Case ID', 'Unknown')
    generation_time = script_data.get('generation_timestamp', 'Unknown')

    # Format timestamp
    if generation_time != 'Unknown':
        try:
            dt = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
            generation_time = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            pass

    # Status indicator
    status_map = {
        "ready": ("🔵", "Ready"),
        "passed": ("✅", "Passed"),
        "failed": ("❌", "Failed")
    }

    status_icon, status_text = status_map.get(execution_status, ("🔵", "Ready"))

    # Display status
    if execution_status == "ready":
        st.info(f"{status_icon} {status_text}")
    elif execution_status == "passed":
        st.success(f"{status_icon} {status_text}")
    elif execution_status == "failed":
        st.error(f"{status_icon} {status_text}")

    # Script information
    st.markdown("### 📄 Script Info")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"""
        **File:** `{filename}`
        **Generated:** {generation_time}
        """)

    with col2:
        objective = target_test_case.get('Test Case Objective', 'Not specified')
        truncated_objective = objective[:60] + '...' if len(objective) > 60 else objective
        st.markdown(f"""
        **Test Case:** {tc_id}
        **Objective:** {truncated_objective}
        """)


def render_execution_controls_header():
    """
    Render the execution controls header.
    """
    st.markdown("""
    <div style="margin: 2rem 0 1.5rem 0;">
        <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
            ⚙️ Execution
        </h3>
    </div>
    """, unsafe_allow_html=True)


# def render_execution_options_card():
#     """
#     Render the execution options card.
#     """
#     st.markdown("""
#     <div class="pro-card" style="margin: 1rem 0; padding: 1.5rem;">
#         <h4 style="color: var(--primary-color); margin: 0 0 1rem 0; font-size: 1.1rem;">
#             🔧 Options
#         </h4>
#     </div>
#     """, unsafe_allow_html=True)


def render_verbose_mode_checkbox(verbose_mode_key):
    """
    Render the verbose mode checkbox.

    Args:
        verbose_mode_key: Key for the verbose mode checkbox

    Returns:
        bool: Verbose mode setting
    """
    return st.checkbox(
        "Verbose Mode",
        value=False,
        key=verbose_mode_key,
        help="Show detailed output"
    )


def render_execution_status_indicator(execution_status):
    """
    Render the execution status indicator.

    Args:
        execution_status: Current execution status
    """
    status_messages = {
        "ready": {
            "icon": "💡",
            "text": "Ready",
            "color": "#2196F3"
        },
        "passed": {
            "icon": "✅",
            "text": "Passed",
            "color": "#4CAF50"
        },
        "failed": {
            "icon": "❌",
            "text": "Failed",
            "color": "#F44336"
        }
    }

    if execution_status in status_messages:
        status_info = status_messages[execution_status]
        st.markdown(f"""
        <div style="
            background: rgba({','.join(str(int(status_info['color'][i:i+2], 16)) for i in (1, 3, 5))}, 0.1);
            border: 1px solid {status_info['color']}40;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        ">
            <span style="font-size: 1.2rem;">{status_info['icon']}</span>
            <span style="color: {status_info['color']}; font-weight: 600;">{status_info['text']}</span>
        </div>
        """, unsafe_allow_html=True)


def render_execution_action_buttons(execution_status, filename, execute_callback, clear_callback):
    """
    Render execution action buttons.

    Args:
        execution_status: Current execution status
        filename: Script filename for button keys
        execute_callback: Callback function for execute button
        clear_callback: Callback function for clear button

    Returns:
        tuple: (execute_clicked, rerun_clicked, clear_clicked)
    """
    execute_clicked = False
    rerun_clicked = False
    clear_clicked = False

    # Button layout
    if execution_status in ["passed", "failed"]:
        # Show all three buttons when script has been executed
        button_col1, button_col2, button_col3 = st.columns([1, 1, 1])

        with button_col1:
            button_type = "primary" if execution_status == "ready" else "secondary"
            execute_clicked = st.button(
                "🚀 Execute",
                use_container_width=True,
                type=button_type,
                key=f"execute_{filename}"
            )

        with button_col2:
            rerun_clicked = st.button(
                "🔄 Re-run",
                use_container_width=True,
                key=f"rerun_{filename}"
            )

        with button_col3:
            clear_clicked = st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                type="secondary"
            )
    else:
        # Show only execute and clear buttons for ready state
        button_col1, button_col2 = st.columns([2, 1])

        with button_col1:
            execute_clicked = st.button(
                "🚀 Execute",
                use_container_width=True,
                type="primary",
                key=f"execute_{filename}"
            )

        with button_col2:
            clear_clicked = st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                type="secondary"
            )

    # Handle button clicks
    if execute_clicked or rerun_clicked:
        if execute_callback:
            execute_callback()

    if clear_clicked:
        if clear_callback:
            clear_callback()

    return execute_clicked, rerun_clicked, clear_clicked


def render_execution_results_header():
    """
    Render the execution results header.
    """
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0 1.5rem 0; width: 100%;">
        <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem; font-size: 1.8rem;">
            📊 Results
        </h2>
    </div>
    """, unsafe_allow_html=True)


def render_execution_results_summary(test_results, target_test_case):
    """
    Render the execution results summary with status-based styling.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
    """
    # Basic execution status
    success = test_results.get('success', False)
    timestamp = test_results.get('timestamp', 'Unknown')

    # Results summary card with status-based styling - full width layout
    status_color = "#4CAF50" if success else "#F44336"
    status_bg = "rgba(76, 175, 80, 0.1)" if success else "rgba(244, 67, 54, 0.1)"
    status_icon = "✅" if success else "❌"
    status_text = "PASSED" if success else "FAILED"

    # Format timestamp for better display
    formatted_timestamp = timestamp
    if timestamp != 'Unknown':
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                formatted_timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_timestamp = str(timestamp)
        except:
            formatted_timestamp = str(timestamp)

    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {status_bg}, rgba(255,255,255,0.05));
        border: 1px solid {status_color}40;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        width: 100%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    ">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 2rem; align-items: center;">
            <div style="
                background: {status_bg};
                color: {status_color};
                padding: 0.8rem 1.8rem;
                border-radius: 30px;
                font-weight: 700;
                font-size: 1.2rem;
                display: flex;
                align-items: center;
                gap: 0.8rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            ">
                <span style="font-size: 1.4rem;">{status_icon}</span>
                {status_text}
            </div>
            <div style="text-align: center; color: var(--text-color-light);">
                <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 0.3rem;">Test Case</div>
                <div style="font-weight: 600; font-size: 1.1rem; color: var(--primary-color);">{target_test_case.get('Test Case ID', 'Unknown')}</div>
            </div>
            <div style="text-align: right; color: var(--text-color-light);">
                <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.3rem;">Completed</div>
                <div style="font-weight: 600; font-size: 1rem;">{formatted_timestamp}</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_execution_metrics_header():
    """
    Render the execution metrics header.
    """
    st.markdown("""
    <div style="margin: 2rem 0 1rem 0;">
        <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
            📈 Metrics
        </h3>
    </div>
    """, unsafe_allow_html=True)


def render_junit_metrics_grid(xml_results):
    """
    Render JUnit XML metrics in a responsive grid layout.

    Args:
        xml_results: Parsed JUnit XML results
    """
    if not xml_results or "summary" not in xml_results:
        return

    summary = xml_results["summary"]

    # Display metrics using responsive grid layout for full width
    total_tests = summary.get("total_tests", 0)
    passed_tests = summary.get("passed_tests", 0)
    failed_tests = summary.get("failed_tests", 0)
    duration = summary.get("duration", 0)

    st.markdown(f"""
    <div style="
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 1.5rem 0;
        width: 100%;
    ">
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(103, 58, 183, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">{total_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Total Tests</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.08), rgba(76, 175, 80, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(76, 175, 80, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;">{passed_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Passed</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.08), rgba(244, 67, 54, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(244, 67, 54, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;">{failed_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Failed</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.08), rgba(255, 152, 0, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(255, 152, 0, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #FF9800; margin-bottom: 0.5rem;">{duration:.2f}s</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Duration</div>
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_execution_output_section(test_results, verbose_mode=False):
    """
    Render the execution output section with stdout/stderr.

    Args:
        test_results: Test execution results
        verbose_mode: Whether to show verbose output
    """
    stdout = test_results.get('stdout', '')
    stderr = test_results.get('stderr', '')

    if verbose_mode and (stdout or stderr):
        st.markdown("### 📋 Output")

        if stdout:
            with st.expander("📤 stdout", expanded=False):
                st.code(stdout, language='text')

        if stderr:
            with st.expander("⚠️ stderr", expanded=False):
                st.code(stderr, language='text')


def render_execution_artifacts_section(test_results, verbose_mode=False):
    """
    Render the execution artifacts section with screenshots and other artifacts.

    Args:
        test_results: Test execution results
        verbose_mode: Whether to show verbose output
    """
    import os
    from pathlib import Path

    # Check for screenshots in test results
    screenshots = test_results.get('screenshots', [])

    # Also check for screenshots in the screenshots directory (fallback)
    if not screenshots:
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            screenshot_files = list(screenshots_dir.glob("*.png"))
            if screenshot_files:
                # Sort by modification time to get the most recent ones
                screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                screenshots = [str(f) for f in screenshot_files[:5]]  # Get up to 5 most recent

    # Display screenshots if any are available
    if screenshots:
        st.markdown("### 📸 Screenshots")

        # Show screenshots in a collapsible section
        screenshot_expanded = verbose_mode  # Expand by default in verbose mode
        with st.expander(f"📸 Captured Screenshots ({len(screenshots)})", expanded=screenshot_expanded):
            for i, screenshot_path in enumerate(screenshots[:3]):  # Show up to 3 screenshots
                if os.path.exists(screenshot_path):
                    st.image(
                        screenshot_path,
                        caption=f"Screenshot {i+1}: {os.path.basename(screenshot_path)}",
                        use_container_width=True
                    )
                    if verbose_mode:
                        st.info(f"Path: {screenshot_path}")
                else:
                    st.warning(f"Screenshot not found: {os.path.basename(screenshot_path)}")

    # Display other artifacts if available
    artifacts = test_results.get('artifacts', {})
    if artifacts and verbose_mode:
        st.markdown("### 📁 Artifacts")
        with st.expander("📁 Test Artifacts", expanded=False):
            for artifact_type, artifact_path in artifacts.items():
                if os.path.exists(artifact_path):
                    st.info(f"**{artifact_type.title()}**: `{artifact_path}`")
                else:
                    st.warning(f"**{artifact_type.title()}**: File not found - `{artifact_path}`")


def render_stage10_footer():
    """
    Render the Stage 10 footer.
    """
    st.markdown("---")
    st.markdown("### 🎮 Script Playground")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: Available at any time.

        **🎯 Template-Based**: Uses optimized script patterns.
        """)

    with col2:
        st.info("""
        **🤖 AI-Powered**: Google AI adaptation.

        **⚡ Independent**: Doesn't affect workflow.
        """)


def render_workflow_navigation():
    """
    Render the workflow navigation section.

    Returns:
        tuple: (stage1_clicked, stage8_clicked, stage9_clicked)
    """
    with st.expander("🧭 Navigation", expanded=False):
        col1, col2, col3 = st.columns(3)

        stage1_clicked = False
        stage8_clicked = False
        stage9_clicked = False

        with col1:
            stage1_clicked = st.button("📁 Stage 1", use_container_width=True)

        with col2:
            stage8_clicked = st.button("🔧 Stage 8", use_container_width=True)

        with col3:
            stage9_clicked = st.button("📜 Stage 9", use_container_width=True)

        return stage1_clicked, stage8_clicked, stage9_clicked


def render_generation_success_display(parsed_script, filename, target_test_case, template_script):
    """
    Render the successful script generation display.

    Args:
        parsed_script: Clean parsed script content
        filename: Generated filename
        target_test_case: Target test case information
        template_script: Template script used
    """
    # Display success message
    st.success("✅ **Script Generated**")

    # Display parsed script
    st.markdown("### 📄 Script")
    st.code(parsed_script, language='python')

    # Download and copy buttons
    col1, col2 = st.columns(2)
    with col1:
        st.download_button(
            label="📥 Download",
            data=parsed_script,
            file_name=filename,
            mime="text/x-python",
            use_container_width=True
        )

    with col2:
        if st.button("📋 Copy", use_container_width=True):
            st.code(parsed_script)
            st.info("Script displayed for copying")

    # Display generation summary
    st.info(f"""
    **Summary:**
    - Template: {template_script.get('test_case_id', 'Unknown')}
    - Target: {target_test_case.get('Test Case ID', 'Unknown')}
    - Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    - File: {filename}
    """)
